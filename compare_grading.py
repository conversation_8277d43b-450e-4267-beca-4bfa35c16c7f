#!/usr/bin/env python3
"""
Script to compare current grading method vs new LLM-based rubric grading
"""

import json
import os
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import pandas as pd
from app import app
from models import db, Question, Part, Topic, Subject, MarkingPoint, Submission
import google.generativeai as genai
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure Gemini
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GradingTimer:
    """Timer class for tracking grading performance"""
    def __init__(self):
        self.start_time = time.time()
        self.steps = []
        self.current_step_start = None
        self.current_step_name = None

    def start_step(self, step_name):
        if self.current_step_start is not None:
            self.end_current_step()

        self.current_step_name = step_name
        self.current_step_start = time.time()

    def end_current_step(self):
        if self.current_step_start is not None:
            duration = time.time() - self.current_step_start
            self.steps.append({
                'name': self.current_step_name,
                'duration_ms': round(duration * 1000, 2),
                'duration_s': round(duration, 3)
            })
            self.current_step_start = None
            self.current_step_name = None

    def get_summary(self):
        if self.current_step_start is not None:
            self.end_current_step()

        total_duration = time.time() - self.start_time
        return {
            'total_duration_ms': round(total_duration * 1000, 2),
            'total_duration_s': round(total_duration, 3),
            'steps': self.steps,
            'step_count': len(self.steps)
        }

# Import the correct grading functions from routes/api.py
# We need to access the functions that are defined inside register_api_routes
def get_api_grading_functions():
    """
    Get the grading functions from routes/api.py
    Since the functions are defined inside register_api_routes, we need to create
    a temporary registration to access them.
    """
    # Create a mock app and register the routes to get access to the functions
    from flask import Flask
    from routes.api import register_api_routes
    import google.generativeai as genai

    # Create temporary app context
    temp_app = Flask(__name__)
    temp_app.config['SECRET_KEY'] = 'temp'

    # Mock the required parameters
    class MockLimiter:
        def limit(self, *args, **kwargs):
            def decorator(f):
                return f
            return decorator

    class MockClient:
        pass

    # Register the routes to get access to the nested functions
    with temp_app.app_context():
        register_api_routes(
            temp_app, db, None, MockLimiter(),
            MockClient(), MockClient(),
            genai.GenerativeModel('gemini-2.5-flash')
        )

        # The functions are now available in the app's view functions
        # We need to extract them from the registered routes
        for rule in temp_app.url_map.iter_rules():
            if rule.endpoint == 'get_git_diff':
                view_func = temp_app.view_functions[rule.endpoint]
                # Extract the nested functions from the closure
                if hasattr(view_func, '__closure__') and view_func.__closure__:
                    for cell in view_func.__closure__:
                        if hasattr(cell.cell_contents, '__name__'):
                            if cell.cell_contents.__name__ == '_calculate_score_and_evaluated_points':
                                return cell.cell_contents

    return None

# For now, we'll use a simpler approach - import the module and access functions directly
def import_api_functions():
    """Import the API functions by temporarily registering routes"""
    try:
        # Import the module
        import routes.api as api_module

        # We'll need to create a way to access the nested functions
        # For now, let's create a reference that we can use
        return api_module
    except ImportError as e:
        logger.error(f"Failed to import API functions: {e}")
        return None

def load_rubrics():
    """Load the generated rubrics from JSON file"""
    try:
        with open('chemistry_rubrics.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ chemistry_rubrics.json not found. Please run generate_rubrics.py first.")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Error loading rubrics JSON: {e}")
        return None

def grade_with_new_method(question_text, rubric, student_answer):
    """Grade using the new LLM-based rubric method"""
    
    prompt = f"""You are an objective grader. Your task is to evaluate a student's answer based on the provided examination question and the detailed objective rubric.

Strictly adhere to the rubric for scoring. Do not introduce external knowledge or subjective judgment.

---
Examination Question:
{question_text}

---
Objective Rubric:
{rubric}

---
Student's Answer:
{student_answer}

---
Please provide your grading output in JSON format with two keys:
1. `score`: An integer representing the total score obtained by the student.
2. `comments`: A string containing the point breakdown for each criterion and a brief explanation for the points awarded or deducted.

Example JSON output:
{{
  "score": 5,
  "comments": "Criterion 1: 2/2 points (correct definition). Criterion 2: 3/3 points (identified all items)."
}}"""

    try:
        model = genai.GenerativeModel('gemini-2.5-flash')
        response = model.generate_content(
            prompt,
            generation_config={
                'response_mime_type': 'application/json'
            }
        )
        
        result = json.loads(response.text)
        return {
            'score': result.get('score', 0),
            'comments': result.get('comments', ''),
            'success': True,
            'error': None
        }
    except Exception as e:
        return {
            'score': 0,
            'comments': '',
            'success': False,
            'error': str(e)
        }

def grade_with_current_method(part, student_answer):
    """Grade using the current marking points method by calling the actual API endpoint"""
    try:
        # Instead of duplicating the grading logic, we'll call the actual API endpoint
        # This ensures we're testing the exact same logic that's used in production

        from flask import Flask
        from routes.api import register_api_routes
        import google.generativeai as genai

        # Create a test app context
        test_app = Flask(__name__)
        test_app.config['SECRET_KEY'] = 'test'
        test_app.config['TESTING'] = True

        # Mock the required dependencies
        class MockLimiter:
            def limit(self, *args, **kwargs):
                def decorator(f):
                    return f
                return decorator

        class MockClient:
            pass

        # Initialize Gemini client
        gemini_model = genai.GenerativeModel('gemini-2.5-flash')

        # Register the API routes to get access to the grading functions
        with test_app.app_context():
            register_api_routes(
                test_app, db, None, MockLimiter(),
                MockClient(), MockClient(), gemini_model
            )

            # Now we can access the registered view functions
            # The grading logic is embedded in the get_git_diff endpoint
            # For now, we'll return a placeholder result indicating we need the actual API

            return {
                'score': 0,
                'evaluated_points': [],
                'success': False,
                'error': 'Cannot access nested grading functions - use actual API endpoint instead',
                'timing': {}
            }

    except Exception as e:
        logger.error(f"Error in grade_with_current_method: {e}")
        return {
            'score': 0,
            'evaluated_points': [],
            'success': False,
            'error': str(e),
            'timing': {}
        }

def get_student_submissions():
    """Get all student submissions for chemistry questions"""
    with app.app_context():
        # Find chemistry subject
        chemistry_subject = Subject.query.filter_by(name='h2-chemistry').first()
        if not chemistry_subject:
            return []

        # Get submissions for chemistry questions
        submissions = db.session.query(Submission).join(Part).join(Question).join(Topic).filter(
            Topic.subject_id == chemistry_subject.id,
            Submission.answer.isnot(None),
            Submission.answer != ''
        ).limit(100).all()  # Increased limit for better testing

        return submissions

def process_single_submission(submission_data, rubrics_data):
    """Process a single submission with both grading methods"""
    submission_id, question_id, part_id, part_description, student_answer, max_score, original_score = submission_data

    # Check if we have a rubric for this question/part
    question_key = str(question_id)
    part_key = str(part_id)

    if question_key not in rubrics_data["rubrics"]:
        return {
            'submission_id': submission_id,
            'error': f'No rubric found for question {question_id}',
            'success': False
        }

    if part_key not in rubrics_data["rubrics"][question_key]["parts"]:
        return {
            'submission_id': submission_id,
            'error': f'No rubric found for part {part_id}',
            'success': False
        }

    rubric = rubrics_data["rubrics"][question_key]["parts"][part_key]["rubric"]

    # Get part object for current method (need to do this in app context)
    with app.app_context():
        part = Part.query.get(part_id)
        if not part:
            return {
                'submission_id': submission_id,
                'error': f'Part {part_id} not found in database',
                'success': False
            }

        # Test current method
        start_time = time.time()
        current_result = grade_with_current_method(part, student_answer)
        current_time = time.time() - start_time

        # Test new method
        start_time = time.time()
        new_result = grade_with_new_method(part_description, rubric, student_answer)
        new_time = time.time() - start_time

        # Prepare detailed feedback for Excel
        current_feedback = ""
        if current_result.get("evaluated_points"):
            feedback_parts = []
            for point in current_result["evaluated_points"]:
                status = "✓" if point.get("achieved") else "✗"
                score_text = f"{point.get('achieved_score', 0)}/{point.get('score', 0)}"
                feedback_parts.append(f"{status} {point.get('description', '')}: {score_text}")
            current_feedback = " | ".join(feedback_parts)

        return {
            'submission_id': submission_id,
            'question_id': question_id,
            'part_id': part_id,
            'question_title': part.question.title if part.question else 'Unknown',
            'part_description': part_description[:100] + "..." if len(part_description) > 100 else part_description,
            'max_score': max_score,
            'student_answer': student_answer[:300] + "..." if len(student_answer) > 300 else student_answer,
            'original_score': original_score,
            'current_score': current_result["score"],
            'current_success': current_result["success"],
            'current_error': current_result.get("error", ""),
            'current_time': current_time,
            'current_feedback': current_feedback,
            'new_score': new_result["score"],
            'new_success': new_result["success"],
            'new_error': new_result.get("error", ""),
            'new_time': new_time,
            'new_feedback': new_result.get("comments", ""),
            'score_difference': abs(current_result["score"] - new_result["score"]) if current_result["success"] and new_result["success"] else None,
            'success': True
        }

def compare_grading_methods():
    """Compare both grading methods on actual student submissions using parallel processing"""

    # Load rubrics
    rubrics_data = load_rubrics()
    if not rubrics_data:
        return

    # Get student submissions
    submissions = get_student_submissions()
    if not submissions:
        print("No student submissions found for chemistry questions")
        return

    print(f"🧪 Found {len(submissions)} chemistry submissions to test")
    print(f"📚 Using {len(rubrics_data['rubrics'])} available rubrics")
    print(f"⚡ Processing with parallel workers...")

    # Prepare submission data for parallel processing (avoid lazy loading issues)
    submission_data_list = []
    with app.app_context():
        for submission in submissions:
            # Eagerly load the part data to avoid session issues
            part = Part.query.get(submission.part_id)
            if part:
                submission_data_list.append((
                    submission.id,
                    submission.question_id,
                    submission.part_id,
                    part.description,
                    submission.answer,
                    part.score,
                    submission.score
                ))

    # Process submissions in parallel
    results = []
    successful_results = []

    with ThreadPoolExecutor(max_workers=5) as executor:
        # Submit all tasks
        future_to_submission = {
            executor.submit(process_single_submission, sub_data, rubrics_data): sub_data[0]
            for sub_data in submission_data_list
        }

        # Collect results as they complete
        for i, future in enumerate(as_completed(future_to_submission), 1):
            submission_id = future_to_submission[future]
            try:
                result = future.result()
                results.append(result)

                if result.get('success', False):
                    successful_results.append(result)
                    print(f"✅ {i}/{len(submissions)} - Submission {submission_id}: Current={result['current_score']:.1f}, New={result['new_score']:.1f}")
                else:
                    print(f"❌ {i}/{len(submissions)} - Submission {submission_id}: {result.get('error', 'Unknown error')}")

            except Exception as e:
                print(f"❌ {i}/{len(submissions)} - Submission {submission_id}: Exception - {str(e)}")
                results.append({
                    'submission_id': submission_id,
                    'error': str(e),
                    'success': False
                })

    print(f"\n📊 Processing complete! {len(successful_results)}/{len(results)} submissions processed successfully")

    # Calculate summary statistics
    both_successful = 0
    current_only_successful = 0
    new_only_successful = 0
    both_failed = 0
    score_differences = []
    current_times = []
    new_times = []

    for result in successful_results:
        current_times.append(result['current_time'])
        new_times.append(result['new_time'])

        if result['current_success'] and result['new_success']:
            both_successful += 1
            if result['score_difference'] is not None:
                score_differences.append(result['score_difference'])
        elif result['current_success']:
            current_only_successful += 1
        elif result['new_success']:
            new_only_successful += 1
        else:
            both_failed += 1

    # Create Excel output
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    excel_filename = f"grading_comparison_{timestamp}.xlsx"

    # Prepare data for Excel
    excel_data = []
    for result in results:
        if result.get('success', False):
            excel_data.append({
                'Submission_ID': result['submission_id'],
                'Question_ID': result['question_id'],
                'Part_ID': result['part_id'],
                'Question_Title': result['question_title'],
                'Part_Description': result['part_description'],
                'Max_Score': result['max_score'],
                'Student_Answer': result['student_answer'],
                'Original_Score': result['original_score'],
                'Current_Method_Score': result['current_score'],
                'Current_Method_Success': result['current_success'],
                'Current_Method_Error': result['current_error'],
                'Current_Method_Time_Seconds': round(result['current_time'], 3),
                'Current_Method_Feedback': result['current_feedback'],
                'New_Method_Score': result['new_score'],
                'New_Method_Success': result['new_success'],
                'New_Method_Error': result['new_error'],
                'New_Method_Time_Seconds': round(result['new_time'], 3),
                'New_Method_Feedback': result['new_feedback'],
                'Score_Difference': result['score_difference'],
                'Methods_Agree': result['current_score'] == result['new_score'] if result['current_success'] and result['new_success'] else False
            })
        else:
            excel_data.append({
                'Submission_ID': result['submission_id'],
                'Error': result.get('error', 'Unknown error'),
                'Success': False
            })

    # Create DataFrame and save to Excel
    df = pd.DataFrame(excel_data)

    with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
        # Main results sheet
        df.to_excel(writer, sheet_name='Comparison_Results', index=False)

        # Summary statistics sheet
        summary_data = {
            'Metric': [
                'Total Submissions Tested',
                'Successfully Processed',
                'Both Methods Successful',
                'Current Method Only Successful',
                'New Method Only Successful',
                'Both Methods Failed',
                'Average Score Difference',
                'Average Current Method Time (s)',
                'Average New Method Time (s)',
                'Speed Improvement Factor'
            ],
            'Value': [
                len(results),
                len(successful_results),
                both_successful,
                current_only_successful,
                new_only_successful,
                both_failed,
                round(sum(score_differences) / len(score_differences), 3) if score_differences else 0,
                round(sum(current_times) / len(current_times), 3) if current_times else 0,
                round(sum(new_times) / len(new_times), 3) if new_times else 0,
                round((sum(current_times) / len(current_times)) / (sum(new_times) / len(new_times)), 2) if current_times and new_times else 0
            ]
        }

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='Summary_Statistics', index=False)

    # Print summary
    print(f"\n🎉 Grading Method Comparison Complete!")
    print(f"=" * 60)
    print(f"📊 Results Summary:")
    print(f"   • Total submissions tested: {len(results)}")
    print(f"   • Successfully processed: {len(successful_results)}")
    print(f"   • Both methods successful: {both_successful}")
    print(f"   • Current method only: {current_only_successful}")
    print(f"   • New method only: {new_only_successful}")
    print(f"   • Both failed: {both_failed}")

    if score_differences:
        avg_diff = sum(score_differences) / len(score_differences)
        print(f"   • Average score difference: {avg_diff:.3f}")

    if current_times and new_times:
        avg_current = sum(current_times) / len(current_times)
        avg_new = sum(new_times) / len(new_times)
        speed_factor = avg_current / avg_new if avg_new > 0 else 0
        print(f"   • Average time - Current: {avg_current:.3f}s")
        print(f"   • Average time - New: {avg_new:.3f}s")
        print(f"   • Speed improvement: {speed_factor:.2f}x")

    print(f"\n📁 Detailed results saved to: {excel_filename}")
    print(f"   • Sheet 1: Comparison_Results (detailed data)")
    print(f"   • Sheet 2: Summary_Statistics (overview)")

    return excel_filename
if __name__ == "__main__":
    print("⚖️  Grading Method Comparison with Parallel Processing")
    print("=" * 60)
    compare_grading_methods()
